# 2025年最佳实践：安全的Celery Worker Dockerfile
# 参考：Docker官方安全指南和Celery最佳实践
FROM python:3.11.13-slim

# 设置工作目录
WORKDIR /app

# 🔧 2025年安全最佳实践：先创建非root用户
# 使用固定的UID/GID，避免权限冲突
RUN groupadd --system --gid 1001 celery && \
    useradd --system --uid 1001 --gid celery --home /app --shell /bin/bash celery

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV REDIS_HOST=redis
ENV TZ=UTC

# 设置时区为UTC（数据库层统一时区重构）
RUN ln -snf /usr/share/zoneinfo/"$TZ" /etc/localtime && echo "$TZ" > /etc/timezone

# 复制依赖文件，设置正确的所有者
COPY --chown=celery:celery pyproject.toml .

# 🔧 最佳实践2：为Playwright指定一个系统级的、与用户无关的安装路径
#    这是解决问题的关键！我们把浏览器安装在 /ms-playwright 这个"公共公园"里。
ENV PLAYWRIGHT_BROWSERS_PATH=/ms-playwright

# 安装Python依赖和Playwright浏览器（在root权限下）
RUN pip install --no-cache-dir -e . && \
    playwright install --with-deps chromium

# 复制所有项目代码，设置正确的所有者
COPY --chown=celery:celery . .

# 创建必要的目录，设置严格的权限
RUN mkdir -p screenshots payment_screenshots downloads .prefs test_data results temp logs && \
    chown -R celery:celery /app && \
    # 🔒 2025年安全最佳实践：严格的文件权限
    find /app -type f -exec chmod 644 {} \; && \
    find /app -type d -exec chmod 755 {} \; && \
    # 🔒 应用代码只读，数据目录可写
    chmod -R 644 /app/backend /app/app /app/celery_worker /app/config && \
    find /app/backend /app/app /app/celery_worker /app/config -type d -exec chmod 755 {} \; && \
    # 🔒 数据目录保持可写权限（将通过tmpfs或volume挂载）
    # 使用750权限提高安全性（只有用户和组可访问）
    chmod -R 750 /app/screenshots \
    /app/payment_screenshots \
    /app/downloads \
    /app/.prefs \
    /app/test_data \
    /app/results \
    /app/temp \
    /app/logs

# 🔧 2025年安全最佳实践：切换到非root用户
# 注意：浏览器已经安装在系统级目录，celery用户只需要读取权限
USER celery

# 🔥 生产级 Celery Worker 启动配置
# 优化并发数、心跳、日志等参数以支持大规模部署

# 🎯 优化配置：减少进程数以加快启动（测试性配置）
CMD ["celery", "-A", "celery_worker.celery_app", "worker", \
    "--loglevel=info", \
    "--pool=prefork", \
    "--concurrency=4", \
    "--heartbeat-interval=30", \
    "--without-gossip", \
    "--without-mingle", \
    "--optimization=fair"]
