# 🚀 生产环境配置 - 用于客户试用和生产部署
# 使用方式：docker compose -f docker-compose.yml -f docker-compose.production.yml up -d
#
# 🎯 生产环境特点：
# - 完全容器化，包括前端
# - 使用nginx统一入口（端口8000）
# - 适合frp隧道暴露到公网
# - 适合客户试用和生产部署
# - 禁用调试功能，优化性能和安全性

services:
  # 前端服务 - 生产环境使用容器化版本
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - VITE_API_BASE_URL=/api  # 使用相对路径，适用于容器环境
    environment:
      - NODE_ENV=production
    networks:
      - app-network

  # Nginx负载均衡器 - 生产环境启用
  nginx-lb:
    image: nginx:latest
    container_name: visa_automator_nginx_lb
    ports:
      - "8000:8000"  # 生产环境使用8000端口（用于frp隧道）
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - visa-automator
      - frontend
    restart: unless-stopped
    networks:
      - app-network

  # 后端服务 - 生产环境配置
  visa-automator:
    # 🔒 2025年生产环境安全标准：只读文件系统 + 安全限制
    read_only: true
    cap_drop:
      - ALL
    # 注意：移除CHOWN能力，只读文件系统不需要
    tmpfs:
      - /app/logs:noexec,nosuid,size=100m,uid=1002,gid=1002
      - /app/temp:noexec,nosuid,size=200m,uid=1002,gid=1002
      - /tmp:noexec,nosuid,size=100m
    volumes:
      - screenshots_data:/app/screenshots:rw
      - downloads_data:/app/downloads:rw
      - payment_screenshots_data:/app/payment_screenshots:rw
      - prefs_data:/app/.prefs:rw
      - test_data:/app/test_data:rw
      - results_data:/app/results:rw
    environment:
      - ENVIRONMENT=production
      - DEBUG=false

  # Celery工作者 - 生产环境配置
  celery-worker:
    # 🔒 2025年生产环境安全标准：只读文件系统 + 安全限制
    read_only: true
    cap_drop:
      - ALL
    # 注意：移除CHOWN能力，只读文件系统不需要
    tmpfs:
      - /app/logs:noexec,nosuid,size=100m,uid=1001,gid=1001
      - /app/temp:noexec,nosuid,size=200m,uid=1001,gid=1001
      - /tmp:noexec,nosuid,size=100m
    volumes:
      - screenshots_data:/app/screenshots:rw
      - downloads_data:/app/downloads:rw
      - payment_screenshots_data:/app/payment_screenshots:rw
      - prefs_data:/app/.prefs:rw
      - test_data:/app/test_data:rw
      - results_data:/app/results:rw
    environment:
      - ENVIRONMENT=production

  # 邮件轮询服务 - 生产环境配置
  email-polling:
    # 🔒 2025年生产环境安全标准：只读文件系统 + 安全限制
    read_only: true
    cap_drop:
      - ALL
    # 注意：移除CHOWN能力，只读文件系统不需要
    tmpfs:
      - /app/logs:noexec,nosuid,size=100m,uid=1003,gid=1003
      - /app/temp:noexec,nosuid,size=100m,uid=1003,gid=1003
      - /tmp:noexec,nosuid,size=100m
    volumes:
      - downloads_data:/app/downloads:rw  # 邮件服务只需要下载目录
    environment:
      - ENVIRONMENT=production

  # E2E测试服务（可选）
  playwright:
    build:
      context: ./frontend
      dockerfile: Dockerfile.playwright
      args:
        - VITE_API_URL=http://nginx-lb:8000
    depends_on:
      - nginx-lb
    environment:
      - CI=true
      - PLAYWRIGHT_HTML_HOST=0.0.0.0
    volumes:
      - ./frontend/test-results:/app/test-results
      - ./frontend/playwright-report:/app/playwright-report
    ports:
      - "9323:9323"  # Playwright报告服务器
    profiles:
      - e2e  # 只在需要E2E测试时启用
    networks:
      - app-network

# 🔧 持久化存储卷（生产环境安全配置需要）
volumes:
  screenshots_data:
    driver: local
  downloads_data:
    driver: local
  payment_screenshots_data:
    driver: local
  prefs_data:
    driver: local
  test_data:
    driver: local
  results_data:
    driver: local
