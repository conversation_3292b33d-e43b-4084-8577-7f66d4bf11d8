"""
邮件处理API端点
===============

提供邮件处理相关的数据库操作API，统一管理邮件处理的数据访问
替代邮件模块直接访问数据库的方式，实现架构统一性

功能：
- 提交确认邮件处理
- 付款确认邮件处理
- 出签结果邮件处理
- PDF下载状态更新
"""

from datetime import datetime
from typing import Any

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, Field
from sqlalchemy import and_, select, update
from sqlalchemy.ext.asyncio import AsyncSession

from app.data.models.applicant import Applicant
from app.data.models.application import Application
from app.data.models.automation_logs import AutomationLogs
from app.data.models.visa_status_history import VisaStatusHistory
from app.utils.logger_config import get_logger
from backend.dependencies import get_db_session

logger = get_logger()
router = APIRouter(prefix="/email-processing", tags=["邮件处理"])

# ===== 请求模型 =====


class SubmissionEmailRequest(BaseModel):
    """提交确认邮件处理请求"""

    full_name: str = Field(..., description="申请人全名")
    date_of_birth: str = Field(..., description="出生日期")
    application_number: str = Field(..., description="越南官方申请编号")
    email_from: str = Field(..., description="邮件发送方")
    email_subject: str = Field(..., description="邮件主题")
    received_time: datetime = Field(..., description="邮件接收时间")


class PaymentEmailRequest(BaseModel):
    """付款确认邮件处理请求"""

    application_number: str = Field(..., description="越南官方申请编号")
    payment_amount: float = Field(..., description="付款金额")
    payment_time: datetime = Field(..., description="付款时间")
    transaction_id: str = Field(..., description="交易ID")
    email_from: str = Field(..., description="邮件发送方")
    email_subject: str = Field(..., description="邮件主题")
    received_time: datetime = Field(..., description="邮件接收时间")


class ResultEmailRequest(BaseModel):
    """出签结果邮件处理请求"""

    application_number: str = Field(..., description="越南官方申请编号")
    download_url: str = Field(..., description="PDF下载链接")
    pdf_save_path: str | None = Field(None, description="PDF保存路径")
    email_from: str = Field(..., description="邮件发送方")
    email_subject: str = Field(..., description="邮件主题")
    received_time: datetime = Field(..., description="邮件接收时间")


# ===== 响应模型 =====


class EmailProcessingResponse(BaseModel):
    """邮件处理响应"""

    success: bool = Field(..., description="处理是否成功")
    message: str = Field(..., description="处理结果消息")
    application_id: str | None = Field(None, description="匹配的申请ID")
    updated_fields: dict[str, Any] | None = Field(None, description="更新的字段")


# ===== API端点 =====


@router.post("/submission-confirmation", response_model=EmailProcessingResponse)
async def process_submission_confirmation(
    request: SubmissionEmailRequest, session: AsyncSession = Depends(get_db_session)
):
    """
    处理提交确认邮件

    根据申请人姓名和出生日期匹配申请记录，更新越南官方申请编号
    """
    try:
        logger.info(
            f"🔍 处理提交确认邮件: {request.full_name}, DOB: {request.date_of_birth}"
        )

        # 根据姓名和出生日期查找申请记录
        query = (
            select(Application, Applicant)
            .join(Applicant, Application.applicant_id == Applicant.id)
            .where(
                and_(
                    Applicant.date_of_birth
                    == datetime.strptime(request.date_of_birth, "%Y-%m-%d").date(),
                    # 支持多种姓名匹配方式
                    (
                        (
                            Applicant.surname + " " + Applicant.given_name
                            == request.full_name
                        )
                        | (
                            Applicant.given_name + " " + Applicant.surname
                            == request.full_name
                        )
                        | (Applicant.chinese_name == request.full_name)
                    ),
                )
            )
        )

        result = await session.execute(query)
        application_applicant = result.first()

        if not application_applicant:
            logger.warning(
                f"⚠️ 未找到匹配的申请记录: {request.full_name}, {request.date_of_birth}"
            )
            return EmailProcessingResponse(
                success=False,
                message=f"未找到匹配的申请记录: {request.full_name}, {request.date_of_birth}",
                application_id=None,
                updated_fields=None,
            )

        application, applicant = application_applicant

        # 更新申请编号
        await session.execute(
            update(Application)
            .where(Application.id == application.id)
            .values(
                application_number=request.application_number,
            )
        )

        # 记录状态历史
        status_history = VisaStatusHistory(
            application_id=application.id,
            visa_status="submitted",
            operator="email_system",
            remark=f"提交确认邮件处理，申请编号: {request.application_number}",
        )
        session.add(status_history)

        await session.commit()

        logger.info(
            f"✅ 提交确认处理成功: {application.id} -> {request.application_number}"
        )

        return EmailProcessingResponse(
            success=True,
            message="提交确认邮件处理成功",
            application_id=str(application.id),
            updated_fields={
                "application_number": request.application_number,
                "status": "confirmed",
            },
        )

    except Exception as e:
        await session.rollback()
        logger.error(f"❌ 提交确认邮件处理失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"提交确认邮件处理失败: {str(e)}")


@router.post("/payment-confirmation", response_model=EmailProcessingResponse)
async def process_payment_confirmation(
    request: PaymentEmailRequest, session: AsyncSession = Depends(get_db_session)
):
    """
    处理付款确认邮件

    根据越南官方申请编号匹配申请记录，更新付款状态
    """
    try:
        logger.info(f"🔍 处理付款确认邮件: {request.application_number}")

        # 根据申请编号查找申请记录
        query = select(Application).where(
            Application.application_number == request.application_number
        )

        result = await session.execute(query)
        application = result.scalar_one_or_none()

        if not application:
            logger.warning(f"⚠️ 未找到匹配的申请记录: {request.application_number}")
            return EmailProcessingResponse(
                success=False,
                message=f"未找到匹配的申请记录: {request.application_number}",
                application_id=None,
                updated_fields=None,
            )

        # 更新自动化日志中的付款状态
        await session.execute(
            update(AutomationLogs)
            .where(AutomationLogs.application_id == application.id)
            .values(
                payment_amount=request.payment_amount,
                payment_time=request.payment_time,
            )
        )

        # 记录状态历史
        status_history = VisaStatusHistory(
            application_id=application.id,
            visa_status="paid",
            operator="email_system",
            remark=f"付款确认邮件处理，金额: ${request.payment_amount}, 交易ID: {request.transaction_id}",
        )
        session.add(status_history)

        await session.commit()

        logger.info(
            f"✅ 付款确认处理成功: {application.id} -> ${request.payment_amount}"
        )

        return EmailProcessingResponse(
            success=True,
            message="付款确认邮件处理成功",
            application_id=str(application.id),
            updated_fields={
                "payment_amount": request.payment_amount,
                "payment_time": request.payment_time.isoformat(),
                "status": "paid",
            },
        )

    except Exception as e:
        await session.rollback()
        logger.error(f"❌ 付款确认邮件处理失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"付款确认邮件处理失败: {str(e)}")


@router.post("/result-notification", response_model=EmailProcessingResponse)
async def process_result_notification(
    request: ResultEmailRequest, session: AsyncSession = Depends(get_db_session)
):
    """
    处理出签结果邮件

    根据越南官方申请编号匹配申请记录，更新PDF下载状态
    """
    try:
        logger.info(f"🔍 处理出签结果邮件: {request.application_number}")

        # 根据申请编号查找申请记录
        query = select(Application).where(
            Application.application_number == request.application_number
        )

        result = await session.execute(query)
        application = result.scalar_one_or_none()

        if not application:
            logger.warning(f"⚠️ 未找到匹配的申请记录: {request.application_number}")
            return EmailProcessingResponse(
                success=False,
                message=f"未找到匹配的申请记录: {request.application_number}",
                application_id=None,
                updated_fields=None,
            )

        # 更新自动化日志中的PDF下载状态
        update_data = {
            "pdf_download_url": request.download_url,
        }

        if request.pdf_save_path:
            update_data["pdf_save_path"] = request.pdf_save_path

        await session.execute(
            update(AutomationLogs)
            .where(AutomationLogs.application_id == application.id)
            .values(**update_data)
        )

        # 记录状态历史
        status_history = VisaStatusHistory(
            application_id=application.id,
            visa_status="approved" if request.pdf_save_path else "ready_for_download",
            operator="email_system",
            remark=f"出签结果邮件处理，PDF: {'已下载' if request.pdf_save_path else '待下载'}",
        )
        session.add(status_history)

        await session.commit()

        logger.info(f"✅ 出签结果处理成功: {application.id} -> {request.download_url}")

        return EmailProcessingResponse(
            success=True,
            message="出签结果邮件处理成功",
            application_id=str(application.id),
            updated_fields={
                "pdf_download_url": request.download_url,
                "pdf_save_path": request.pdf_save_path,
                "status": "approved" if request.pdf_save_path else "ready_for_download",
            },
        )

    except Exception as e:
        await session.rollback()
        logger.error(f"❌ 出签结果邮件处理失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"出签结果邮件处理失败: {str(e)}")


# ===== 查询和工具端点 =====


@router.get("/application/{application_number}")
async def get_application_by_number(
    application_number: str, session: AsyncSession = Depends(get_db_session)
):
    """
    根据越南官方申请编号查询申请信息

    用于邮件处理前的验证和调试
    """
    try:
        query = (
            select(Application, Applicant)
            .join(Applicant, Application.applicant_id == Applicant.id)
            .where(Application.application_number == application_number)
        )

        result = await session.execute(query)
        application_applicant = result.first()

        if not application_applicant:
            raise HTTPException(
                status_code=404, detail=f"未找到申请记录: {application_number}"
            )

        application, applicant = application_applicant

        return {
            "application_id": str(application.id),
            "application_number": application.application_number,
            "applicant_name": f"{applicant.surname} {applicant.given_name}",
            "chinese_name": applicant.chinese_name or "Unknown",
            "date_of_birth": applicant.date_of_birth.isoformat()
            if applicant.date_of_birth
            else None,
            "email": applicant.email,
            "created_at": application.created_at.isoformat(),
            "updated_at": application.updated_at.isoformat(),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 查询申请信息失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"查询申请信息失败: {str(e)}")


# 移除冗余的模块级健康检查端点
# 原因：遵循Docker健康检查最佳实践，统一使用全局/health端点
# 避免多个健康检查端点造成的架构混乱
