FROM python:3.11.11-slim

# 设置工作目录
WORKDIR /app

# 🔧 2025年安全最佳实践：先创建非root用户
# 使用固定的UID/GID，避免权限冲突
RUN groupadd --system --gid 1002 fastapi && \
    useradd --system --uid 1002 --gid fastapi --home /app --shell /bin/bash fastapi



# 复制依赖文件
COPY pyproject.toml .

# 先安装基础依赖（不包括当前项目),后端FastAPI不需要 Playwright，只安装基础依赖
RUN pip install --no-cache-dir build

# 复制应用代码
COPY . .

# 🔧 Docker最佳实践：安装项目、创建目录并设置严格权限
# 单个RUN指令减少镜像层数，确保权限设置的原子性
RUN pip install --no-cache-dir -e . && \
    apt-get update && apt-get install -y --no-install-recommends curl && \
    apt-get clean && rm -rf /var/lib/apt/lists/* && \
    mkdir -p screenshots payment_screenshots downloads .prefs test_data results temp logs && \
    chown -R fastapi:fastapi /app && \
    # 🔒 2025年安全最佳实践：严格的文件权限
    find /app -type f -exec chmod 644 {} \; && \
    find /app -type d -exec chmod 755 {} \; && \
    # 🔒 应用代码只读，数据目录可写
    chmod -R 644 /app/backend /app/app /app/celery_worker /app/config && \
    find /app/backend /app/app /app/celery_worker /app/config -type d -exec chmod 755 {} \; && \
    # 🔒 数据目录保持可写权限（将通过tmpfs或volume挂载）
    # 使用750权限提高安全性（只有用户和组可访问）
    chmod -R 750 /app/screenshots \
    /app/payment_screenshots \
    /app/downloads \
    /app/.prefs \
    /app/test_data \
    /app/results \
    /app/temp \
    /app/logs

# 设置环境变量
ENV PYTHONUNBUFFERED=1


# 设置时区为UTC（数据库层统一时区重构）
ENV TZ=UTC
RUN ln -snf /usr/share/zoneinfo/"$TZ" /etc/localtime && echo "$TZ" > /etc/timezone

# 🔧 2025年安全最佳实践：切换到非root用户
USER fastapi

# 健康检查端点 开发阶段 取消健康检查



# 默认启动新版FastAPI (可被docker-compose.yml覆盖)
CMD ["uvicorn", "backend.main:app", "--host", "0.0.0.0", "--port", "8000"]
